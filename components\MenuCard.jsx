import React from "react";
import {
  softgetraenke,
  shishas,
  saefte,
  heissgetraenke,
  bier,
  cocktails,
  mocktailsOhneAlkohol,
  aperitif,
  likoere,
  spritz,
  vodka,
  whiskys,
  rum,
  gin,
  wein,
  prosecco,
  champagner,
  shots,
  speisen,
  snacks,
} from "./menu";
import { Box, Container, Divider, Typography } from "@mui/material";
import Image from "next/image";
import pxToRem from "@/ThemeRegistry/pxToRem";

function MenuCard() {
  return (
    <>
      <Typography
        variant="h1"
        component={"h2"}
        textAlign={"center"}
        sx={{ fontSize: { xs: 30, sm: 40 }, py: 5 }}
        color="text"
      >
        MENÜKARTE
      </Typography>
      <section id="shisha">
        <Container
          sx={{
            border: "2px solid #67f756",
            borderRadius: 1,
            position: "relative",
          }}
        >
          <Box
            sx={{
              position: "relative",
              height: "100%",
              width: "100%",
              // background: "url(https://images.ctfassets.net/87jhdyn6f199/7N1VhkwVLqtntIfrcND2L8/625e9e9bca13e0e8d60f47e6142a7e69/shisha.png)",
              background:
                "url(https://images.ctfassets.net/87jhdyn6f199/66R8uiPd4OQdR9fwYdFKBi/9e616a6997c7e728f3aeccea83f4cef2/IMG_9793.JPG)",
              backgroundPosition: "center",
              backgroundSize: "cover",
              backgroundRepeat: "no-repeat",
              borderRadius: 1,
            }}
          >
            <Box
              sx={{
                position: "absolute",
                top: 0,
                left: 0,
                height: "100%",
                width: "100%",
                backgroundColor: "rgba(0, 0, 0, 0.7)",
                borderRadius: 1,
              }}
            />
            <Box sx={{ position: "relative" }}>
              <Box
                display={"flex"}
                justifyContent={"center"}
                alignItems={"center"}
                flexWrap={"wrap"}
                gap={2}
              >
                <Image
                  src={"/assets/offer.png"}
                  alt="Winteraktion"
                  width={50}
                  height={50}
                />
                <Typography
                  variant="h1"
                  component={"h2"}
                  sx={{
                    fontSize: `clamp(${pxToRem(20)}, 7vw, ${pxToRem(25)})`,
                  }}
                >
                  Winteraktion
                </Typography>
              </Box>
              <Typography
                variant="h1"
                component={"h2"}
                textAlign={"center"}
                sx={{ fontSize: { xs: 25, sm: 30 }, my: 2 }}
                color="text"
              >
                {shishas.section}
              </Typography>
              {shishas.items.map((item, index) => {
                const { code, name, price, newPrice } = item;
                return (
                  <Box
                    key={index}
                    display={"flex"}
                    justifyContent={"space-between"}
                    alignItems={"center"}
                    gap={2}
                    my={{ xs: 1, md: 2 }}
                  >
                    <Box>
                      <Box
                        sx={{ display: "flex", alignItems: "center", gap: 0.5 }}
                      >
                        <Typography
                          variant="body1"
                          component={"p"}
                          sx={{ fontSize: { xs: 14, sm: 18 } }}
                          color="text"
                        >
                          {name}
                        </Typography>
                        {"-"}
                        <Typography
                          variant="body1"
                          display={"block"}
                          sx={{ fontSize: { xs: 14, sm: 16 } }}
                          color={"#b4d1b0"}
                        >
                          {code}
                        </Typography>
                      </Box>
                    </Box>
                    <Box flexGrow={1}>
                      <Divider
                        variant="fullWidth"
                        sx={{
                          borderBottomWidth: "1px",
                          borderStyle: "solid",
                          borderColor: "#67f756",
                          opacity: 0.6,
                        }}
                      />
                    </Box>
                    {newPrice !== null ? (
                      <>
                        <Box>
                          <Typography
                            variant="body1"
                            sx={{
                              fontSize: { xs: 14, sm: 16 },
                              textDecoration: "line-through",
                              color: "red",
                            }}
                            color="text"
                          >
                            {price.toFixed(2)}
                          </Typography>
                        </Box>
                        <Box>
                          <Typography
                            variant="body1"
                            sx={{ fontSize: { xs: 16, sm: 18 } }}
                            color="text"
                          >
                            {newPrice.toFixed(2)}
                          </Typography>
                        </Box>
                      </>
                    ) : (
                      <Box>
                        <Typography
                          variant="body1"
                          sx={{ fontSize: { xs: 16, sm: 18 } }}
                          color="text"
                        >
                          {price.toFixed(2)}
                        </Typography>
                      </Box>
                    )}
                  </Box>
                );
              })}
            </Box>
          </Box>
        </Container>
      </section>
      <section id="getranke">
        <Typography
          variant="h1"
          component={"h2"}
          textAlign={"center"}
          sx={{ fontSize: { xs: 30, sm: 40 }, py: 5 }}
          color="text"
        >
          GETRÄNKE
        </Typography>
        <Container
          sx={{
            border: "2px solid #67f756",
            borderRadius: 1,
            position: "relative",
          }}
        >
          <Box
            sx={{
              position: "relative",
              height: "100%",
              width: "100%",
              background: `url(${softgetraenke.imageUrl})`,
              backgroundPosition: "center",
              backgroundSize: "cover",
              backgroundRepeat: "no-repeat",
              borderRadius: 1,
            }}
          >
            <Box
              sx={{
                position: "absolute",
                top: 0,
                left: 0,
                height: "100%",
                width: "100%",
                backgroundColor: "rgba(0, 0, 0, 0.7)",
                borderRadius: 1,
              }}
            />
            <Box sx={{ position: "relative" }}>
              <Typography
                variant="h1"
                component={"h2"}
                textAlign={"center"}
                sx={{ fontSize: { xs: 25, sm: 30 }, my: 2 }}
                color="text"
              >
                {softgetraenke.section}
              </Typography>
              {softgetraenke.items.map((item, index) => {
                const { code, name, price } = item;
                return (
                  <Box
                    key={index}
                    display={"flex"}
                    justifyContent={"space-between"}
                    alignItems={"center"}
                    gap={2}
                    my={{ xs: 1, md: 2 }}
                  >
                    <Box>
                      <Box
                        sx={{ display: "flex", alignItems: "center", gap: 0.5 }}
                      >
                        <Typography
                          variant="body1"
                          component={"p"}
                          sx={{ fontSize: { xs: 14, sm: 18 } }}
                          color="text"
                        >
                          {name}
                        </Typography>
                        {"-"}
                        <Typography
                          variant="body1"
                          display={"block"}
                          sx={{ fontSize: { xs: 14, sm: 16 } }}
                          color={"#b4d1b0"}
                        >
                          {code}
                        </Typography>
                      </Box>
                    </Box>
                    <Box flexGrow={1}>
                      <Divider
                        variant="fullWidth"
                        sx={{
                          borderBottomWidth: "1px",
                          borderStyle: "solid",
                          borderColor: "#67f756",
                          opacity: 0.6,
                        }}
                      />
                    </Box>
                    <Box>
                      <Typography
                        variant="body1"
                        sx={{ fontSize: { xs: 14, sm: 16 } }}
                        color="text"
                      >
                        {price.toFixed(2)}
                      </Typography>
                    </Box>
                  </Box>
                );
              })}
            </Box>
          </Box>
        </Container>
        <Container
          sx={{
            border: "2px solid #67f756",
            borderRadius: 1,
            position: "relative",
            my: 5,
          }}
        >
          <Box sx={{ position: "relative" }}>
            <Typography
              variant="h1"
              component={"h2"}
              textAlign={"center"}
              sx={{ fontSize: { xs: 25, sm: 30 }, my: 2 }}
              color="text"
            >
              {saefte.section}
            </Typography>
            {saefte.items.map((item, index) => {
              const { code, name, price } = item;
              return (
                <Box
                  key={index}
                  display={"flex"}
                  justifyContent={"space-between"}
                  alignItems={"center"}
                  gap={2}
                  my={{ xs: 1, md: 2 }}
                >
                  <Box>
                    <Box
                      sx={{ display: "flex", alignItems: "center", gap: 0.5 }}
                    >
                      <Typography
                        variant="body1"
                        component={"p"}
                        sx={{ fontSize: { xs: 14, sm: 18 } }}
                        color="text"
                      >
                        {name}
                      </Typography>
                      {"-"}
                      <Typography
                        variant="body1"
                        display={"block"}
                        sx={{ fontSize: { xs: 14, sm: 16 } }}
                        color={"#b4d1b0"}
                      >
                        {code}
                      </Typography>
                    </Box>
                  </Box>
                  <Box flexGrow={1}>
                    <Divider
                      variant="fullWidth"
                      sx={{
                        borderBottomWidth: "1px",
                        borderStyle: "solid",
                        borderColor: "#67f756",
                        opacity: 0.6,
                      }}
                    />
                  </Box>
                  <Box>
                    <Typography
                      variant="body1"
                      sx={{ fontSize: { xs: 14, sm: 16 } }}
                      color="text"
                    >
                      {price.toFixed(2)}
                    </Typography>
                  </Box>
                </Box>
              );
            })}
          </Box>
        </Container>
        <Container
          sx={{
            border: "2px solid #67f756",
            borderRadius: 1,
            position: "relative",
          }}
        >
          <Box
            sx={{
              position: "relative",
              height: "100%",
              width: "100%",
              background: `url(${heissgetraenke.imageUrl})`,
              backgroundPosition: "center",
              backgroundSize: "cover",
              backgroundRepeat: "no-repeat",
              borderRadius: 1,
            }}
          >
            <Box
              sx={{
                position: "absolute",
                top: 0,
                left: 0,
                height: "100%",
                width: "100%",
                backgroundColor: "rgba(0, 0, 0, 0.7)",
                borderRadius: 1,
              }}
            />
            <Box sx={{ position: "relative" }}>
              <Typography
                variant="h1"
                component={"h2"}
                textAlign={"center"}
                sx={{ fontSize: { xs: 25, sm: 30 }, my: 2 }}
                color="text"
              >
                {heissgetraenke.section}
              </Typography>
              {heissgetraenke.items.map((item, index) => {
                const { code, name, price } = item;
                return (
                  <Box
                    key={index}
                    display={"flex"}
                    justifyContent={"space-between"}
                    alignItems={"center"}
                    gap={2}
                    my={{ xs: 1, md: 2 }}
                  >
                    <Box>
                      <Box
                        sx={{ display: "flex", alignItems: "center", gap: 0.5 }}
                      >
                        <Typography
                          variant="body1"
                          component={"p"}
                          sx={{ fontSize: { xs: 14, sm: 18 } }}
                          color="text"
                        >
                          {name}
                        </Typography>
                        {"-"}
                        <Typography
                          variant="body1"
                          display={"block"}
                          sx={{ fontSize: { xs: 14, sm: 16 } }}
                          color={"#b4d1b0"}
                        >
                          {code}
                        </Typography>
                      </Box>
                    </Box>
                    <Box flexGrow={1}>
                      <Divider
                        variant="fullWidth"
                        sx={{
                          borderBottomWidth: "1px",
                          borderStyle: "solid",
                          borderColor: "#67f756",
                          opacity: 0.6,
                        }}
                      />
                    </Box>
                    <Box>
                      <Typography
                        variant="body1"
                        sx={{ fontSize: { xs: 14, sm: 16 } }}
                        color="text"
                      >
                        {price.toFixed(2)}
                      </Typography>
                    </Box>
                  </Box>
                );
              })}
            </Box>
          </Box>
        </Container>
        <Container
          sx={{
            border: "2px solid #67f756",
            borderRadius: 1,
            position: "relative",
            my: 5,
          }}
        >
          <Box sx={{ position: "relative" }}>
            <Typography
              variant="h1"
              component={"h2"}
              textAlign={"center"}
              sx={{ fontSize: { xs: 25, sm: 30 }, my: 2 }}
              color="text"
            >
              {bier.section}
            </Typography>
            {bier.items.map((item, index) => {
              const { code, name, price } = item;
              return (
                <Box
                  key={index}
                  display={"flex"}
                  justifyContent={"space-between"}
                  alignItems={"center"}
                  gap={2}
                  my={{ xs: 1, md: 2 }}
                >
                  <Box>
                    <Box
                      sx={{ display: "flex", alignItems: "center", gap: 0.5 }}
                    >
                      <Typography
                        variant="body1"
                        component={"p"}
                        sx={{ fontSize: { xs: 14, sm: 18 } }}
                        color="text"
                      >
                        {name}
                      </Typography>
                      {"-"}
                      <Typography
                        variant="body1"
                        display={"block"}
                        sx={{ fontSize: { xs: 14, sm: 16 } }}
                        color={"#b4d1b0"}
                      >
                        {code}
                      </Typography>
                    </Box>
                  </Box>
                  <Box flexGrow={1}>
                    <Divider
                      variant="fullWidth"
                      sx={{
                        borderBottomWidth: "1px",
                        borderStyle: "solid",
                        borderColor: "#67f756",
                        opacity: 0.6,
                      }}
                    />
                  </Box>
                  <Box>
                    <Typography
                      variant="body1"
                      sx={{ fontSize: { xs: 14, sm: 16 } }}
                      color="text"
                    >
                      {price.toFixed(2)}
                    </Typography>
                  </Box>
                </Box>
              );
            })}
          </Box>
        </Container>
        <Container
          sx={{
            border: "2px solid #67f756",
            borderRadius: 1,
            position: "relative",
          }}
        >
          <Box
            sx={{
              position: "relative",
              height: "100%",
              width: "100%",
              background: `url(${cocktails.imageUrl})`,
              backgroundPosition: "center",
              backgroundSize: "cover",
              backgroundRepeat: "no-repeat",
              borderRadius: 1,
            }}
          >
            <Box
              sx={{
                position: "absolute",
                top: 0,
                left: 0,
                height: "100%",
                width: "100%",
                backgroundColor: "rgba(0, 0, 0, 0.7)",
                borderRadius: 1,
              }}
            />
            <Box sx={{ position: "relative" }}>
              <Typography
                variant="h1"
                component={"h2"}
                textAlign={"center"}
                sx={{ fontSize: { xs: 25, sm: 30 }, my: 2 }}
                color="text"
              >
                {cocktails.section}
              </Typography>
              {cocktails.items.map((item, index) => {
                const { code, name, price } = item;
                return (
                  <Box
                    key={index}
                    display={"flex"}
                    justifyContent={"space-between"}
                    alignItems={"center"}
                    gap={2}
                    my={{ xs: 1, md: 2 }}
                  >
                    <Box>
                      <Box
                        sx={{ display: "flex", alignItems: "center", gap: 0.5 }}
                      >
                        <Typography
                          variant="body1"
                          component={"p"}
                          sx={{ fontSize: { xs: 14, sm: 18 } }}
                          color="text"
                        >
                          {name}
                        </Typography>
                        {"-"}
                        <Typography
                          variant="body1"
                          display={"block"}
                          sx={{ fontSize: { xs: 14, sm: 16 } }}
                          color={"#b4d1b0"}
                        >
                          {code}
                        </Typography>
                      </Box>
                    </Box>
                    <Box flexGrow={1}>
                      <Divider
                        variant="fullWidth"
                        sx={{
                          borderBottomWidth: "1px",
                          borderStyle: "solid",
                          borderColor: "#67f756",
                          opacity: 0.6,
                        }}
                      />
                    </Box>
                    <Box>
                      <Typography
                        variant="body1"
                        sx={{ fontSize: { xs: 14, sm: 16 } }}
                        color="text"
                      >
                        {price.toFixed(2)}
                      </Typography>
                    </Box>
                  </Box>
                );
              })}
            </Box>
          </Box>
        </Container>
        <Container
          sx={{
            border: "2px solid #67f756",
            borderRadius: 1,
            position: "relative",
            my: 5,
          }}
        >
          <Box sx={{ position: "relative" }}>
            <Typography
              variant="h1"
              component={"h2"}
              textAlign={"center"}
              sx={{ fontSize: { xs: 25, sm: 30 }, my: 2 }}
              color="text"
            >
              {mocktailsOhneAlkohol.section}
            </Typography>
            {mocktailsOhneAlkohol.items.map((item, index) => {
              const { code, name, price } = item;
              return (
                <Box
                  key={index}
                  display={"flex"}
                  justifyContent={"space-between"}
                  alignItems={"center"}
                  gap={2}
                  my={{ xs: 1, md: 2 }}
                >
                  <Box>
                    <Box
                      sx={{ display: "flex", alignItems: "center", gap: 0.5 }}
                    >
                      <Typography
                        variant="body1"
                        component={"p"}
                        sx={{ fontSize: { xs: 14, sm: 18 } }}
                        color="text"
                      >
                        {name}
                      </Typography>
                      {"-"}
                      <Typography
                        variant="body1"
                        display={"block"}
                        sx={{ fontSize: { xs: 14, sm: 16 } }}
                        color={"#b4d1b0"}
                      >
                        {code}
                      </Typography>
                    </Box>
                  </Box>
                  <Box flexGrow={1}>
                    <Divider
                      variant="fullWidth"
                      sx={{
                        borderBottomWidth: "1px",
                        borderStyle: "solid",
                        borderColor: "#67f756",
                        opacity: 0.6,
                      }}
                    />
                  </Box>
                  <Box>
                    <Typography
                      variant="body1"
                      sx={{ fontSize: { xs: 14, sm: 16 } }}
                      color="text"
                    >
                      {price.toFixed(2)}
                    </Typography>
                  </Box>
                </Box>
              );
            })}
          </Box>
        </Container>
        <Container
          sx={{
            border: "2px solid #67f756",
            borderRadius: 1,
            position: "relative",
            my: 5,
          }}
        >
          <Box sx={{ position: "relative" }}>
            <Typography
              variant="h1"
              component={"h2"}
              textAlign={"center"}
              sx={{ fontSize: { xs: 25, sm: 30 }, my: 2 }}
              color="text"
            >
              {aperitif.section}
            </Typography>
            {aperitif.items.map((item, index) => {
              const { code, name, price } = item;
              return (
                <Box
                  key={index}
                  display={"flex"}
                  justifyContent={"space-between"}
                  alignItems={"center"}
                  gap={2}
                  my={{ xs: 1, md: 2 }}
                >
                  <Box>
                    <Box
                      sx={{ display: "flex", alignItems: "center", gap: 0.5 }}
                    >
                      <Typography
                        variant="body1"
                        component={"p"}
                        sx={{ fontSize: { xs: 14, sm: 18 } }}
                        color="text"
                      >
                        {name}
                      </Typography>
                      {"-"}
                      <Typography
                        variant="body1"
                        display={"block"}
                        sx={{ fontSize: { xs: 14, sm: 16 } }}
                        color={"#b4d1b0"}
                      >
                        {code}
                      </Typography>
                    </Box>
                  </Box>
                  <Box flexGrow={1}>
                    <Divider
                      variant="fullWidth"
                      sx={{
                        borderBottomWidth: "1px",
                        borderStyle: "solid",
                        borderColor: "#67f756",
                        opacity: 0.6,
                      }}
                    />
                  </Box>
                  <Box>
                    <Typography
                      variant="body1"
                      sx={{ fontSize: { xs: 14, sm: 16 } }}
                      color="text"
                    >
                      {price.toFixed(2)}
                    </Typography>
                  </Box>
                </Box>
              );
            })}
          </Box>
        </Container>
        <Container
          sx={{
            border: "2px solid #67f756",
            borderRadius: 1,
            position: "relative",
            my: 5,
          }}
        >
          <Box sx={{ position: "relative" }}>
            <Typography
              variant="h1"
              component={"h2"}
              textAlign={"center"}
              sx={{ fontSize: { xs: 25, sm: 30 }, my: 2 }}
              color="text"
            >
              {likoere.section}
            </Typography>
            {likoere.items.map((item, index) => {
              const { code, name, price } = item;
              return (
                <Box
                  key={index}
                  display={"flex"}
                  justifyContent={"space-between"}
                  alignItems={"center"}
                  gap={2}
                  my={{ xs: 1, md: 2 }}
                >
                  <Box>
                    <Box
                      sx={{ display: "flex", alignItems: "center", gap: 0.5 }}
                    >
                      <Typography
                        variant="body1"
                        component={"p"}
                        sx={{ fontSize: { xs: 14, sm: 18 } }}
                        color="text"
                      >
                        {name}
                      </Typography>
                      {"-"}
                      <Typography
                        variant="body1"
                        display={"block"}
                        sx={{ fontSize: { xs: 14, sm: 16 } }}
                        color={"#b4d1b0"}
                      >
                        {code}
                      </Typography>
                    </Box>
                  </Box>
                  <Box flexGrow={1}>
                    <Divider
                      variant="fullWidth"
                      sx={{
                        borderBottomWidth: "1px",
                        borderStyle: "solid",
                        borderColor: "#67f756",
                        opacity: 0.6,
                      }}
                    />
                  </Box>
                  <Box>
                    <Typography
                      variant="body1"
                      sx={{ fontSize: { xs: 14, sm: 16 } }}
                      color="text"
                    >
                      {price.toFixed(2)}
                    </Typography>
                  </Box>
                </Box>
              );
            })}
          </Box>
        </Container>
        <Container
          sx={{
            border: "2px solid #67f756",
            borderRadius: 1,
            position: "relative",
            my: 5,
          }}
        >
          <Box sx={{ position: "relative" }}>
            <Typography
              variant="h1"
              component={"h2"}
              textAlign={"center"}
              sx={{ fontSize: { xs: 25, sm: 30 }, my: 2 }}
              color="text"
            >
              {spritz.section}
            </Typography>
            {spritz.items.map((item, index) => {
              const { code, name, price } = item;
              return (
                <Box
                  key={index}
                  display={"flex"}
                  justifyContent={"space-between"}
                  alignItems={"center"}
                  gap={2}
                  my={{ xs: 1, md: 2 }}
                >
                  <Box>
                    <Box
                      sx={{ display: "flex", alignItems: "center", gap: 0.5 }}
                    >
                      <Typography
                        variant="body1"
                        component={"p"}
                        sx={{ fontSize: { xs: 14, sm: 18 } }}
                        color="text"
                      >
                        {name}
                      </Typography>
                      {"-"}
                      <Typography
                        variant="body1"
                        display={"block"}
                        sx={{ fontSize: { xs: 14, sm: 16 } }}
                        color={"#b4d1b0"}
                      >
                        {code}
                      </Typography>
                    </Box>
                  </Box>
                  <Box flexGrow={1}>
                    <Divider
                      variant="fullWidth"
                      sx={{
                        borderBottomWidth: "1px",
                        borderStyle: "solid",
                        borderColor: "#67f756",
                        opacity: 0.6,
                      }}
                    />
                  </Box>
                  <Box>
                    <Typography
                      variant="body1"
                      sx={{ fontSize: { xs: 14, sm: 16 } }}
                      color="text"
                    >
                      {price.toFixed(2)}
                    </Typography>
                  </Box>
                </Box>
              );
            })}
          </Box>
        </Container>
        <Container
          sx={{
            border: "2px solid #67f756",
            borderRadius: 1,
            position: "relative",
            my: 5,
          }}
        >
          <Box sx={{ position: "relative" }}>
            <Typography
              variant="h1"
              component={"h2"}
              textAlign={"center"}
              sx={{ fontSize: { xs: 25, sm: 30 }, my: 2 }}
              color="text"
            >
              {vodka.section}
            </Typography>
            {vodka.items.map((item, index) => {
              const { code, name, price } = item;
              return (
                <Box
                  key={index}
                  display={"flex"}
                  justifyContent={"space-between"}
                  alignItems={"center"}
                  gap={2}
                  my={{ xs: 1, md: 2 }}
                >
                  <Box>
                    <Box
                      sx={{ display: "flex", alignItems: "center", gap: 0.5 }}
                    >
                      <Typography
                        variant="body1"
                        component={"p"}
                        sx={{ fontSize: { xs: 14, sm: 18 } }}
                        color="text"
                      >
                        {name}
                      </Typography>
                      {"-"}
                      <Typography
                        variant="body1"
                        display={"block"}
                        sx={{ fontSize: { xs: 14, sm: 16 } }}
                        color={"#b4d1b0"}
                      >
                        {code}
                      </Typography>
                    </Box>
                  </Box>
                  <Box flexGrow={1}>
                    <Divider
                      variant="fullWidth"
                      sx={{
                        borderBottomWidth: "1px",
                        borderStyle: "solid",
                        borderColor: "#67f756",
                        opacity: 0.6,
                      }}
                    />
                  </Box>
                  <Box>
                    <Typography
                      variant="body1"
                      sx={{ fontSize: { xs: 14, sm: 16 } }}
                      color="text"
                    >
                      {price.toFixed(2)}
                    </Typography>
                  </Box>
                </Box>
              );
            })}
          </Box>
        </Container>
        <Container
          sx={{
            border: "2px solid #67f756",
            borderRadius: 1,
            position: "relative",
            my: 5,
          }}
        >
          <Box sx={{ position: "relative" }}>
            <Typography
              variant="h1"
              component={"h2"}
              textAlign={"center"}
              sx={{ fontSize: { xs: 25, sm: 30 }, my: 2 }}
              color="text"
            >
              {whiskys.section}
            </Typography>
            {whiskys.items.map((item, index) => {
              const { code, name, price } = item;
              return (
                <Box
                  key={index}
                  display={"flex"}
                  justifyContent={"space-between"}
                  alignItems={"center"}
                  gap={2}
                  my={{ xs: 1, md: 2 }}
                >
                  <Box>
                    <Box
                      sx={{ display: "flex", alignItems: "center", gap: 0.5 }}
                    >
                      <Typography
                        variant="body1"
                        component={"p"}
                        sx={{ fontSize: { xs: 14, sm: 18 } }}
                        color="text"
                      >
                        {name}
                      </Typography>
                      {"-"}
                      <Typography
                        variant="body1"
                        display={"block"}
                        sx={{ fontSize: { xs: 14, sm: 16 } }}
                        color={"#b4d1b0"}
                      >
                        {code}
                      </Typography>
                    </Box>
                  </Box>
                  <Box flexGrow={1}>
                    <Divider
                      variant="fullWidth"
                      sx={{
                        borderBottomWidth: "1px",
                        borderStyle: "solid",
                        borderColor: "#67f756",
                        opacity: 0.6,
                      }}
                    />
                  </Box>
                  <Box>
                    <Typography
                      variant="body1"
                      sx={{ fontSize: { xs: 14, sm: 16 } }}
                      color="text"
                    >
                      {price.toFixed(2)}
                    </Typography>
                  </Box>
                </Box>
              );
            })}
          </Box>
        </Container>
        <Container
          sx={{
            border: "2px solid #67f756",
            borderRadius: 1,
            position: "relative",
            my: 5,
          }}
        >
          <Box sx={{ position: "relative" }}>
            <Typography
              variant="h1"
              component={"h2"}
              textAlign={"center"}
              sx={{ fontSize: { xs: 25, sm: 30 }, my: 2 }}
              color="text"
            >
              {rum.section}
            </Typography>
            {rum.items.map((item, index) => {
              const { code, name, price } = item;
              return (
                <Box
                  key={index}
                  display={"flex"}
                  justifyContent={"space-between"}
                  alignItems={"center"}
                  gap={2}
                  my={{ xs: 1, md: 2 }}
                >
                  <Box>
                    <Box
                      sx={{ display: "flex", alignItems: "center", gap: 0.5 }}
                    >
                      <Typography
                        variant="body1"
                        component={"p"}
                        sx={{ fontSize: { xs: 14, sm: 18 } }}
                        color="text"
                      >
                        {name}
                      </Typography>
                      {"-"}
                      <Typography
                        variant="body1"
                        display={"block"}
                        sx={{ fontSize: { xs: 14, sm: 16 } }}
                        color={"#b4d1b0"}
                      >
                        {code}
                      </Typography>
                    </Box>
                  </Box>
                  <Box flexGrow={1}>
                    <Divider
                      variant="fullWidth"
                      sx={{
                        borderBottomWidth: "1px",
                        borderStyle: "solid",
                        borderColor: "#67f756",
                        opacity: 0.6,
                      }}
                    />
                  </Box>
                  <Box>
                    <Typography
                      variant="body1"
                      sx={{ fontSize: { xs: 14, sm: 16 } }}
                      color="text"
                    >
                      {price.toFixed(2)}
                    </Typography>
                  </Box>
                </Box>
              );
            })}
          </Box>
        </Container>
        <Container
          sx={{
            border: "2px solid #67f756",
            borderRadius: 1,
            position: "relative",
            my: 5,
          }}
        >
          <Box sx={{ position: "relative" }}>
            <Typography
              variant="h1"
              component={"h2"}
              textAlign={"center"}
              sx={{ fontSize: { xs: 25, sm: 30 }, my: 2 }}
              color="text"
            >
              {gin.section}
            </Typography>
            {gin.items.map((item, index) => {
              const { code, name, price } = item;
              return (
                <Box
                  key={index}
                  display={"flex"}
                  justifyContent={"space-between"}
                  alignItems={"center"}
                  gap={2}
                  my={{ xs: 1, md: 2 }}
                >
                  <Box>
                    <Box
                      sx={{ display: "flex", alignItems: "center", gap: 0.5 }}
                    >
                      <Typography
                        variant="body1"
                        component={"p"}
                        sx={{ fontSize: { xs: 14, sm: 18 } }}
                        color="text"
                      >
                        {name}
                      </Typography>
                      {"-"}
                      <Typography
                        variant="body1"
                        display={"block"}
                        sx={{ fontSize: { xs: 14, sm: 16 } }}
                        color={"#b4d1b0"}
                      >
                        {code}
                      </Typography>
                    </Box>
                  </Box>
                  <Box flexGrow={1}>
                    <Divider
                      variant="fullWidth"
                      sx={{
                        borderBottomWidth: "1px",
                        borderStyle: "solid",
                        borderColor: "#67f756",
                        opacity: 0.6,
                      }}
                    />
                  </Box>
                  <Box>
                    <Typography
                      variant="body1"
                      sx={{ fontSize: { xs: 14, sm: 16 } }}
                      color="text"
                    >
                      {price.toFixed(2)}
                    </Typography>
                  </Box>
                </Box>
              );
            })}
          </Box>
        </Container>
        <Container
          sx={{
            border: "2px solid #67f756",
            borderRadius: 1,
            position: "relative",
            my: 5,
          }}
        >
          <Box sx={{ position: "relative" }}>
            <Typography
              variant="h1"
              component={"h2"}
              textAlign={"center"}
              sx={{ fontSize: { xs: 25, sm: 30 }, my: 2 }}
              color="text"
            >
              {wein.section}
            </Typography>
            {wein.items.map((item, index) => {
              const { code, name, price } = item;
              return (
                <Box
                  key={index}
                  display={"flex"}
                  justifyContent={"space-between"}
                  alignItems={"center"}
                  gap={2}
                  my={{ xs: 1, md: 2 }}
                >
                  <Box>
                    <Box
                      sx={{ display: "flex", alignItems: "center", gap: 0.5 }}
                    >
                      <Typography
                        variant="body1"
                        component={"p"}
                        sx={{ fontSize: { xs: 14, sm: 18 } }}
                        color="text"
                      >
                        {name}
                      </Typography>
                      {"-"}
                      <Typography
                        variant="body1"
                        display={"block"}
                        sx={{ fontSize: { xs: 14, sm: 16 } }}
                        color={"#b4d1b0"}
                      >
                        {code}
                      </Typography>
                    </Box>
                  </Box>
                  <Box flexGrow={1}>
                    <Divider
                      variant="fullWidth"
                      sx={{
                        borderBottomWidth: "1px",
                        borderStyle: "solid",
                        borderColor: "#67f756",
                        opacity: 0.6,
                      }}
                    />
                  </Box>
                  <Box>
                    <Typography
                      variant="body1"
                      sx={{ fontSize: { xs: 14, sm: 16 } }}
                      color="text"
                    >
                      {price.toFixed(2)}
                    </Typography>
                  </Box>
                </Box>
              );
            })}
          </Box>
        </Container>
        <Container
          sx={{
            border: "2px solid #67f756",
            borderRadius: 1,
            position: "relative",
            my: 5,
          }}
        >
          <Box sx={{ position: "relative" }}>
            <Typography
              variant="h1"
              component={"h2"}
              textAlign={"center"}
              sx={{ fontSize: { xs: 25, sm: 30 }, my: 2 }}
              color="text"
            >
              {prosecco.section}
            </Typography>
            {prosecco.items.map((item, index) => {
              const { code, name, price } = item;
              return (
                <Box
                  key={index}
                  display={"flex"}
                  justifyContent={"space-between"}
                  alignItems={"center"}
                  gap={2}
                  my={{ xs: 1, md: 2 }}
                >
                  <Box>
                    <Box
                      sx={{ display: "flex", alignItems: "center", gap: 0.5 }}
                    >
                      <Typography
                        variant="body1"
                        component={"p"}
                        sx={{ fontSize: { xs: 14, sm: 18 } }}
                        color="text"
                      >
                        {name}
                      </Typography>
                      {"-"}
                      <Typography
                        variant="body1"
                        display={"block"}
                        sx={{ fontSize: { xs: 14, sm: 16 } }}
                        color={"#b4d1b0"}
                      >
                        {code}
                      </Typography>
                    </Box>
                  </Box>
                  <Box flexGrow={1}>
                    <Divider
                      variant="fullWidth"
                      sx={{
                        borderBottomWidth: "1px",
                        borderStyle: "solid",
                        borderColor: "#67f756",
                        opacity: 0.6,
                      }}
                    />
                  </Box>
                  <Box>
                    <Typography
                      variant="body1"
                      sx={{ fontSize: { xs: 14, sm: 16 } }}
                      color="text"
                    >
                      {price.toFixed(2)}
                    </Typography>
                  </Box>
                </Box>
              );
            })}
          </Box>
        </Container>
        <Container
          sx={{
            border: "2px solid #67f756",
            borderRadius: 1,
            position: "relative",
            my: 5,
          }}
        >
          <Box sx={{ position: "relative" }}>
            <Typography
              variant="h1"
              component={"h2"}
              textAlign={"center"}
              sx={{ fontSize: { xs: 25, sm: 30 }, my: 2 }}
              color="text"
            >
              {champagner.section}
            </Typography>
            {champagner.items.map((item, index) => {
              const { code, name, price } = item;
              return (
                <Box
                  key={index}
                  display={"flex"}
                  justifyContent={"space-between"}
                  alignItems={"center"}
                  gap={2}
                  my={{ xs: 1, md: 2 }}
                >
                  <Box>
                    <Box
                      sx={{ display: "flex", alignItems: "center", gap: 0.5 }}
                    >
                      <Typography
                        variant="body1"
                        component={"p"}
                        sx={{ fontSize: { xs: 14, sm: 18 } }}
                        color="text"
                      >
                        {name}
                      </Typography>
                      {"-"}
                      <Typography
                        variant="body1"
                        display={"block"}
                        sx={{ fontSize: { xs: 14, sm: 16 } }}
                        color={"#b4d1b0"}
                      >
                        {code}
                      </Typography>
                    </Box>
                  </Box>
                  <Box flexGrow={1}>
                    <Divider
                      variant="fullWidth"
                      sx={{
                        borderBottomWidth: "1px",
                        borderStyle: "solid",
                        borderColor: "#67f756",
                        opacity: 0.6,
                      }}
                    />
                  </Box>
                  <Box>
                    <Typography
                      variant="body1"
                      sx={{ fontSize: { xs: 14, sm: 16 } }}
                      color="text"
                    >
                      {price.toFixed(2)}
                    </Typography>
                  </Box>
                </Box>
              );
            })}
          </Box>
        </Container>
        <Container
          sx={{
            border: "2px solid #67f756",
            borderRadius: 1,
            position: "relative",
            my: 5,
          }}
        >
          <Box sx={{ position: "relative" }}>
            <Typography
              variant="h1"
              component={"h2"}
              textAlign={"center"}
              sx={{ fontSize: { xs: 25, sm: 30 }, my: 2 }}
              color="text"
            >
              {shots.section}
            </Typography>
            {shots.items.map((item, index) => {
              const { code, name, price } = item;
              return (
                <Box
                  key={index}
                  display={"flex"}
                  justifyContent={"space-between"}
                  alignItems={"center"}
                  gap={2}
                  my={{ xs: 1, md: 2 }}
                >
                  <Box>
                    <Box
                      sx={{ display: "flex", alignItems: "center", gap: 0.5 }}
                    >
                      <Typography
                        variant="body1"
                        component={"p"}
                        sx={{ fontSize: { xs: 14, sm: 18 } }}
                        color="text"
                      >
                        {name}
                      </Typography>
                      {"-"}
                      <Typography
                        variant="body1"
                        display={"block"}
                        sx={{ fontSize: { xs: 14, sm: 16 } }}
                        color={"#b4d1b0"}
                      >
                        {code}
                      </Typography>
                    </Box>
                  </Box>
                  <Box flexGrow={1}>
                    <Divider
                      variant="fullWidth"
                      sx={{
                        borderBottomWidth: "1px",
                        borderStyle: "solid",
                        borderColor: "#67f756",
                        opacity: 0.6,
                      }}
                    />
                  </Box>
                  <Box>
                    <Typography
                      variant="body1"
                      sx={{ fontSize: { xs: 14, sm: 16 } }}
                      color="text"
                    >
                      {price.toFixed(2)}
                    </Typography>
                  </Box>
                </Box>
              );
            })}
          </Box>
        </Container>
      </section>
      <section id="speisen">
        <Typography
          variant="h1"
          component={"h2"}
          textAlign={"center"}
          sx={{ fontSize: { xs: 30, sm: 40 }, py: 5 }}
          color="text"
        >
          SPEISEN
        </Typography>
        <Container
          sx={{
            border: "2px solid #67f756",
            borderRadius: 1,
            position: "relative",
            my: 5,
          }}
        >
          <Box sx={{ position: "relative" }}>
            <Typography
              variant="h1"
              component={"h2"}
              textAlign={"center"}
              sx={{ fontSize: { xs: 25, sm: 30 }, my: 2 }}
              color="text"
            >
              {speisen.section}
            </Typography>
            {speisen.items.map((item, index) => {
              const { code, name, price } = item;
              return (
                <Box
                  key={index}
                  display={"flex"}
                  justifyContent={"space-between"}
                  alignItems={"center"}
                  gap={2}
                  my={{ xs: 1, md: 2 }}
                >
                  <Box>
                    <Box
                      sx={{ display: "flex", alignItems: "center", gap: 0.5 }}
                    >
                      <Typography
                        variant="body1"
                        component={"p"}
                        sx={{ fontSize: { xs: 14, sm: 18 } }}
                        color="text"
                      >
                        {name}
                      </Typography>
                      {"-"}
                      <Typography
                        variant="body1"
                        display={"block"}
                        sx={{ fontSize: { xs: 14, sm: 16 } }}
                        color={"#b4d1b0"}
                      >
                        {code}
                      </Typography>
                    </Box>
                  </Box>
                  <Box flexGrow={1}>
                    <Divider
                      variant="fullWidth"
                      sx={{
                        borderBottomWidth: "1px",
                        borderStyle: "solid",
                        borderColor: "#67f756",
                        opacity: 0.6,
                      }}
                    />
                  </Box>
                  <Box>
                    <Typography
                      variant="body1"
                      sx={{ fontSize: { xs: 14, sm: 16 } }}
                      color="text"
                    >
                      {price.toFixed(2)}
                    </Typography>
                  </Box>
                </Box>
              );
            })}
          </Box>
        </Container>
      </section>
      <section id="snacks">
        <Container
          sx={{
            border: "2px solid #67f756",
            borderRadius: 1,
            position: "relative",
            my: 5,
          }}
        >
          <Box sx={{ position: "relative" }}>
            <Typography
              variant="h1"
              component={"h2"}
              textAlign={"center"}
              sx={{ fontSize: { xs: 25, sm: 30 }, my: 2 }}
              color="text"
            >
              {snacks.section}
            </Typography>
            {snacks.items.map((item, index) => {
              const { code, name, price } = item;
              return (
                <Box
                  key={index}
                  display={"flex"}
                  justifyContent={"space-between"}
                  alignItems={"center"}
                  gap={2}
                  my={{ xs: 1, md: 2 }}
                >
                  <Box>
                    <Box
                      sx={{ display: "flex", alignItems: "center", gap: 0.5 }}
                    >
                      <Typography
                        variant="body1"
                        component={"p"}
                        sx={{ fontSize: { xs: 14, sm: 18 } }}
                        color="text"
                      >
                        {name}
                      </Typography>
                      {"-"}
                      <Typography
                        variant="body1"
                        display={"block"}
                        sx={{ fontSize: { xs: 14, sm: 16 } }}
                        color={"#b4d1b0"}
                      >
                        {code}
                      </Typography>
                    </Box>
                  </Box>
                  <Box flexGrow={1}>
                    <Divider
                      variant="fullWidth"
                      sx={{
                        borderBottomWidth: "1px",
                        borderStyle: "solid",
                        borderColor: "#67f756",
                        opacity: 0.6,
                      }}
                    />
                  </Box>
                  <Box>
                    <Typography
                      variant="body1"
                      sx={{ fontSize: { xs: 14, sm: 16 } }}
                      color="text"
                    >
                      {price.toFixed(2)}
                    </Typography>
                  </Box>
                </Box>
              );
            })}
          </Box>
        </Container>
      </section>
    </>
  );
}

export default MenuCard;
