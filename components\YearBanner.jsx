"use client";
import * as React from "react";
import Stack from "@mui/material/Stack";
import TrapFocus from "@mui/material/Unstable_TrapFocus";
import Paper from "@mui/material/Paper";
import Fade from "@mui/material/Fade";
import Button from "@mui/material/Button";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import Link from "next/link";

export default function YearBanner() {
  const [bannerOpen, setBannerOpen] = React.useState(true);

  const closeBanner = () => {
    setBannerOpen(false);
  };

  return (
    <React.Fragment>
      <TrapFocus open disableAutoFocus disableEnforceFocus>
        <Fade appear={false} in={bannerOpen}>
          <Paper
            role="dialog"
            aria-modal="false"
            aria-label="New Year banner"
            square
            variant="outlined"
            tabIndex={-1}
            sx={{
              position: "fixed",
              bottom: 0,
              left: 0,
              right: 0,
              m: 0,
              p: 2,
              borderWidth: 0,
              borderTopWidth: 1,
              zIndex: 1000,
            }}
          >
            <Stack
              direction={{ xs: "column", sm: "row" }}
              justifyContent="space-between"
              gap={2}
            >
              <Box
                sx={{
                  flexShrink: 1,
                  alignSelf: { xs: "flex-start", sm: "center" },
                }}
              >
                <Typography variant="h6" fontWeight="bold">
                  New Year Party 🎄
                </Typography>
                <Typography variant="body2">
                  Erlebe eine luxuriöse Nacht mit atemberaubender Aussicht,
                  Live-DJ, exklusiven Shisha-Varianten und einer einzigartigen
                  Atmosphäre, um stilvoll ins neue Jahr zu starten. 🥂
                </Typography>
              </Box>
              <Stack
                gap={2}
                direction={{
                  xs: "row-reverse",
                  sm: "row",
                }}
                sx={{
                  flexShrink: 0,
                  alignSelf: { xs: "flex-end", sm: "center" },
                }}
              >
                <Link href={"/kontakt"}>
                  <Button size="small" variant="outlined" sx={{ fontSize: 14 }}>
                    Jetzt Reservieren
                  </Button>
                </Link>
                <Button
                  size="small"
                  onClick={closeBanner}
                  variant="contained"
                  sx={{ fontSize: 14, bgcolor: "primary.dark" }}
                >
                  Close
                </Button>
              </Stack>
            </Stack>
          </Paper>
        </Fade>
      </TrapFocus>
    </React.Fragment>
  );
}
