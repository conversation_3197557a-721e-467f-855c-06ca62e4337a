/* eslint-disable react/jsx-props-no-spreading */
/* eslint-disable react/display-name */
import React from "react";
import "./navigation.css";
import * as NavigationMenu from "@radix-ui/react-navigation-menu";
import PropTypes from "prop-types";
// import { MdOutlineKeyboardArrowDown } from "react-icons/md";
import Link from "next/link";
import { Typography } from "@mui/material";

const Navigation = () => {
  return (
    <NavigationMenu.Root className="NavigationMenuRoot">
      <NavigationMenu.List className="NavigationMenuList">
        <NavigationMenu.Item>
          <Link href="/" className="NavigationMenuLink">
            Home
          </Link>
        </NavigationMenu.Item>
        {/* <NavigationMenu.Item>
          <NavigationMenu.Trigger className="NavigationMenuTrigger">
            Menükarte <MdOutlineKeyboardArrowDown className="CaretDown" aria-hidden />
          </NavigationMenu.Trigger>
          <NavigationMenu.Content className="NavigationMenuContent">
            <ul className="List one">
              <li style={{ gridRow: "span 3" }}>
                <NavigationMenu.Link asChild>
                  <Link className="Callout" href="/menuekarte" />
                </NavigationMenu.Link>
              </li>
              <ListItem href="/menuekarte#shisha" title="Shishas">
              Entdecke üsi erlesene Shishas für es unvergesslichs Raucherlebnis!
              </ListItem>
              <ListItem href="/menuekarte#getranke" title="Getränke">
              Gönn dir üsi erfrischende Getränk, vo klassische Cocktails bis zu erlesene Wyy
              </ListItem>
              <ListItem href="/menuekarte#speisen" title="Speisen">
              Probiere üsi feine Speise, zubereitet mit frische Zuetate und Liebi zum Detail.
              </ListItem>
            </ul>
          </NavigationMenu.Content>
        </NavigationMenu.Item> */}
        <NavigationMenu.Item>
          <Link href="/gallerie" className="NavigationMenuLink">
            Gallerie
          </Link>
        </NavigationMenu.Item>

        <NavigationMenu.Item>
          <Link href="/uber-uns" className="NavigationMenuLink">
           Über uns
          </Link>
        </NavigationMenu.Item>

        <NavigationMenu.Item>
          <Link href="/kontakt" className="NavigationMenuLink">
            Kontakt
          </Link>
        </NavigationMenu.Item>

        <NavigationMenu.Indicator className="NavigationMenuIndicator">
          <div className="Arrow" />
        </NavigationMenu.Indicator>
      </NavigationMenu.List>

      <div className="ViewportPosition">
        <NavigationMenu.Viewport className="NavigationMenuViewport" />
      </div>
    </NavigationMenu.Root>
  );
};

const ListItem = React.forwardRef(({ children, title, ...props }, forwardedRef) => (
  <li>
    <NavigationMenu.Link asChild>
      <a {...props} ref={forwardedRef}>
        <div className="ListItemHeading">{title}</div>
        <Typography variant="body2" className="ListItemText">{children}</Typography>
      </a>
    </NavigationMenu.Link>
  </li>
));

ListItem.propTypes = {
  children: PropTypes.node,
  title: PropTypes.string.isRequired
};

export default Navigation;
