"use client";

import React, { useState } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import TextField from "@mui/material/TextField";
import MenuItem from "@mui/material/MenuItem";
import {
  Alert,
  Box,
  Container,
  Grid,
  InputAdornment,
  Typography,
} from "@mui/material";
import pxToRem from "@/ThemeRegistry/pxToRem";
import { sendReserveForm } from "@/lib/api";
import { LoadingButton } from "@mui/lab";
import { BsSend } from "react-icons/bs";

const validationSchema = Yup.object({
  name: Yup.string().required("Erforderlich"),
  Nachname: Yup.string().required("Erforderlich"),
  Reservierungsart: Yup.string().required("Erforderlich"),
  guests: Yup.number().required("Erforderlich"),
  Datum: Yup.string().required("Dieses Feld ist erforderlich"),
  Uhr: Yup.string().required("Dieses Feld ist erforderlich"),
  email: Yup.string()
    .email("Ungültige E-Mail-Adresse")
    .required("Erforderlich"),
  Telefon: Yup.string()
    .matches(/^\d+$/, "Ungültige Telefonnummer")
    .required("Erforderlich"),
  Notiz: Yup.string(),
});

const options = [
  {
    value: "Reservieren Sie einen Tisch",
    label: "Reservieren Sie einen Tisch",
  },
  { value: "Geburtstagsfeier", label: "Geburtstagsfeier" },
  { value: "Spezielles Ereignis", label: "Spezielles Ereignis" },
];

const ReserveForm = () => {
  const [submitMessage, setSubmitMessage] = useState();
  const [loading, setLoading] = useState(false);
  const formik = useFormik({
    initialValues: {
      name: "",
      Nachname: "",
      Reservierungsart: "",
      guests: "",
      Datum: "",
      Uhr: "",
      email: "",
      Telefon: "",
      Notiz: "",
    },
    validationSchema: validationSchema,
    onSubmit: async (values) => {
      try {
        setLoading(true);
        const response = await sendReserveForm(values);
        if (response.status === 200) {
          // Handle successful response here
          setLoading(false);
          setSubmitMessage(
            <Alert
              sx={{
                fontSize: {
                  xs: "15px",
                  sm: "17px",
                  md: "20px",
                },
                fontWeight: "600",
              }}
            >
              Vielen Dank für Ihre Anfrage! <br /> Wir werden uns umgehend bei
              Ihnen melden
            </Alert>
          );
        } else {
          // Handle error response here
          setSubmitMessage(`error ${response.status}: ${response.statusText}`);
        }
      } catch (error) {
        // Handle network error here
        setSubmitMessage(`Ein Fehler ist aufgetreten: ${error}`);
      }
    },
  });

  return (
    <Container id="form" sx={{ mt: 6, mb: 6 }}>
      <Box
        sx={{
          position: "relative",
          background: "rgba(255, 255, 255, 0.05)",
          backdropFilter: "blur(15px)",
          border: "1px solid rgba(255, 255, 255, 0.1)",
          borderRadius: "24px",
          p: { xs: 3, md: 5 },
          boxShadow: "0 8px 32px rgba(0, 0, 0, 0.3)",
          overflow: "hidden",
        }}
      >
        {/* Modern geometric background */}
        <Box
          sx={{
            position: "absolute",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            background: `
              linear-gradient(45deg, transparent 30%, rgba(103, 247, 86, 0.01) 30%, rgba(103, 247, 86, 0.01) 32%, transparent 32%),
              linear-gradient(-45deg, transparent 30%, rgba(103, 247, 86, 0.01) 30%, rgba(103, 247, 86, 0.01) 32%, transparent 32%)
            `,
            backgroundSize: "40px 40px",
            zIndex: 1,
          }}
        />

        {!submitMessage ? (
          <Box sx={{ position: "relative", zIndex: 2 }}>
            {/* Modern badge */}
            <Box
              sx={{
                display: "inline-flex",
                alignItems: "center",
                gap: 1,
                backgroundColor: "rgba(103, 247, 86, 0.1)",
                border: "1px solid rgba(103, 247, 86, 0.3)",
                borderRadius: "50px",
                px: 3,
                py: 1,
                mb: 3,
                backdropFilter: "blur(10px)",
              }}
            >
              <Typography
                variant="body2"
                sx={{
                  color: "#67f756",
                  fontWeight: "600",
                  fontSize: { xs: "0.9rem", md: "1rem" },
                  letterSpacing: "0.5px",
                }}
              >
                Tisch Reservierung
              </Typography>
            </Box>

            <Typography
              variant="h1"
              component="h2"
              textAlign={{ xs: "center", md: "left" }}
              gutterBottom
              pb={2}
              sx={{
                fontSize: `clamp(${pxToRem(32)}, 7vw, ${pxToRem(45)})`,
                mb: 4,
              }}
            >
              Reservierung
            </Typography>
            <form onSubmit={formik.handleSubmit}>
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <TextField
                    id="name"
                    name="name"
                    label="Name"
                    variant="outlined"
                    fullWidth
                    margin="dense"
                    {...formik.getFieldProps("name")}
                    error={formik.touched.name && Boolean(formik.errors.name)}
                    helperText={formik.touched.name && formik.errors.name}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    id="Nachname"
                    name="Nachname"
                    label="Nachname"
                    variant="outlined"
                    fullWidth
                    margin="dense"
                    {...formik.getFieldProps("Nachname")}
                    error={
                      formik.touched.Nachname && Boolean(formik.errors.Nachname)
                    }
                    helperText={
                      formik.touched.Nachname && formik.errors.Nachname
                    }
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    id="Reservierungsart"
                    name="Reservierungsart"
                    label="Reservierungsart"
                    select
                    variant="outlined"
                    fullWidth
                    margin="dense"
                    {...formik.getFieldProps("Reservierungsart")}
                    error={
                      formik.touched.Reservierungsart &&
                      Boolean(formik.errors.Reservierungsart)
                    }
                    helperText={
                      formik.touched.Reservierungsart &&
                      formik.errors.Reservierungsart
                    }
                  >
                    {options.map((option) => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </TextField>
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    id="guests"
                    name="guests"
                    label="Anzahl der Gäste"
                    variant="outlined"
                    fullWidth
                    margin="dense"
                    type="number"
                    {...formik.getFieldProps("guests")}
                    error={
                      formik.touched.guests && Boolean(formik.errors.guests)
                    }
                    helperText={formik.touched.guests && formik.errors.guests}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Datum"
                    name="Datum"
                    type="date"
                    margin="dense"
                    InputProps={{
                      startAdornment: <InputAdornment position="start" />,
                    }}
                    {...formik.getFieldProps("Datum")}
                    error={formik.touched.Datum && Boolean(formik.errors.Datum)}
                    helperText={formik.touched.Datum && formik.errors.Datum}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Uhr"
                    name="Uhr"
                    type="time"
                    margin="dense"
                    InputProps={{
                      startAdornment: <InputAdornment position="start" />,
                    }}
                    {...formik.getFieldProps("Uhr")}
                    error={formik.touched.Uhr && Boolean(formik.errors.Uhr)}
                    helperText={formik.touched.Uhr && formik.errors.Uhr}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    id="email"
                    name="email"
                    label="E-Mail"
                    variant="outlined"
                    fullWidth
                    margin="dense"
                    {...formik.getFieldProps("email")}
                    error={formik.touched.email && Boolean(formik.errors.email)}
                    helperText={formik.touched.email && formik.errors.email}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    id="Telefon"
                    name="Telefon"
                    label="Telefon"
                    variant="outlined"
                    fullWidth
                    margin="dense"
                    {...formik.getFieldProps("Telefon")}
                    error={
                      formik.touched.Telefon && Boolean(formik.errors.Telefon)
                    }
                    helperText={formik.touched.Telefon && formik.errors.Telefon}
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    id="Notiz"
                    name="Notiz"
                    label="Notiz"
                    variant="outlined"
                    fullWidth
                    multiline
                    rows={5}
                    margin="dense"
                    {...formik.getFieldProps("Notiz")}
                    error={formik.touched.Notiz && Boolean(formik.errors.Notiz)}
                    helperText={formik.touched.Notiz && formik.errors.Notiz}
                  />
                </Grid>
              </Grid>
              <Typography
                variant="caption"
                component={"h6"}
                color={"grey"}
                fontSize={13}
                my={1}
              >
                Hinweis: Wenn Sie zusätzliche Dienstleistungen benötigen,
                kontaktieren Sie uns bitte
                <Typography
                  variant="caption"
                  color={"primary.light"}
                  fontSize={13}
                  pl={1}
                >
                  <a href="tel:+41763652300">+41 76 365 23 00</a>
                </Typography>
              </Typography>
              <LoadingButton
                type="submit"
                endIcon={<BsSend />}
                loading={loading}
                loadingPosition="end"
                variant="contained"
                size="large"
                sx={{
                  px: 6,
                  py: 2,
                  fontSize: "1.1rem",
                  mt: 2,
                }}
              >
                <Typography>Reservierung senden</Typography>
              </LoadingButton>
            </form>
          </Box>
        ) : (
          <Box
            sx={{ position: "relative", zIndex: 2, textAlign: "center", py: 4 }}
          >
            {submitMessage}
          </Box>
        )}
      </Box>
    </Container>
  );
};

export default ReserveForm;
