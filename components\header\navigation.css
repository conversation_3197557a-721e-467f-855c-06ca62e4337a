/* @import "@radix-ui/colors/black-alpha.css";
@import "@radix-ui/colors/indigo.css";
@import "@radix-ui/colors/mauve.css";
@import "@radix-ui/colors/purple.css";
@import "@radix-ui/colors/violet.css"; */

/* reset */
button,
p {
  all: unset;
}

.NavigationMenuRoot {
  position: relative;
  display: flex;
  justify-content: center;
  /* width: 100vw; */
  z-index: 1;
}

.NavigationMenuList {
  display: flex;
  justify-content: center;
  background-color: transparent;
  padding: 4px;
  border-radius: 6px;
  list-style: none;
  margin: 0;
  gap: 20px;
}

.NavigationMenuTrigger,
.NavigationMenuLink {
  padding: 12px 20px;
  outline: none;
  user-select: none;
  font-weight: 600;
  line-height: 1;
  border-radius: 50px;
  font-size: 16px;
  color: #fff;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  letter-spacing: 0.5px;
}
/* .NavigationMenuTrigger:focus,
.NavigationMenuLink:focus {
  box-shadow: 0 0 0 2px #4a1d1f;
} */
.NavigationMenuTrigger:hover .CaretDown,
.NavigationMenuTrigger:hover,
.NavigationMenuLink:hover {
  color: #67f756;
  background: rgba(103, 247, 86, 0.1);
  border: 1px solid rgba(103, 247, 86, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(103, 247, 86, 0.2);
}

.NavigationMenuTrigger {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 2px;
}

.NavigationMenuLink {
  display: block;
  text-decoration: none;
  font-size: 16px;
  font-weight: 500;
  line-height: 1;
  cursor: pointer;
}

.NavigationMenuContent {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  animation-duration: 250ms;
  animation-timing-function: ease;
}
.NavigationMenuContent[data-motion="from-start"] {
  animation-name: enterFromLeft;
}
.NavigationMenuContent[data-motion="from-end"] {
  animation-name: enterFromRight;
}
.NavigationMenuContent[data-motion="to-start"] {
  animation-name: exitToLeft;
}
.NavigationMenuContent[data-motion="to-end"] {
  animation-name: exitToRight;
}
@media only screen and (min-width: 600px) {
  .NavigationMenuContent {
    width: auto;
  }
}

.NavigationMenuIndicator {
  display: flex;
  align-items: flex-end;
  justify-content: center;
  height: 10px;
  top: 100%;
  overflow: hidden;
  z-index: 1;
  transition:
    width,
    transform 250ms ease;
}
.NavigationMenuIndicator[data-state="visible"] {
  animation: fadeIn 200ms ease;
}
.NavigationMenuIndicator[data-state="hidden"] {
  animation: fadeOut 200ms ease;
}

.NavigationMenuViewport {
  position: relative;
  transform-origin: top center;
  margin-top: 10px;
  width: 100%;
  background-color: #fff;
  border-radius: 6px;
  overflow: hidden;
  box-shadow:
    hsl(206 22% 7% / 35%) 0px 10px 38px -10px,
    hsl(206 22% 7% / 20%) 0px 10px 20px -15px;
  height: var(--radix-navigation-menu-viewport-height);
  transition:
    width,
    height,
    300ms ease;
}
.NavigationMenuViewport[data-state="open"] {
  animation: scaleIn 200ms ease;
}
.NavigationMenuViewport[data-state="closed"] {
  animation: scaleOut 200ms ease;
}
@media only screen and (min-width: 600px) {
  .NavigationMenuViewport {
    width: var(--radix-navigation-menu-viewport-width);
  }
}

.List {
  display: grid;
  padding: 22px;
  margin: 0;
  column-gap: 20px;
  list-style: none;
}
@media only screen and (min-width: 600px) {
  .List.one {
    width: 500px;
    grid-template-columns: 0.75fr 1fr;
  }
  .List.two {
    width: 600px;
    grid-auto-flow: column;
    grid-template-rows: repeat(3, 1fr);
  }
}

.ListItemLink {
  display: block;
  outline: none;
  text-decoration: none;
  user-select: none;
  padding: 12px;
  border-radius: 6px;
  font-size: 15px;
  line-height: 1;
}
.ListItemLink:focus {
  box-shadow: 0 0 0 2px #4a1d1f;
}
.ListItemLink:hover {
  background-color: #fdf9ed;
}

.ListItemHeading {
  font-weight: 500;
  line-height: 1.2;
  margin-bottom: 5px;
  margin-top: 5px;
  color: #272727;
}

.ListItemText {
  color: #272727;
  line-height: 1.4;
  font-weight: initial;
}

.Callout {
  display: flex;
  justify-content: flex-end;
  flex-direction: column;
  width: 100%;
  height: 100%;
  /* background: linear-gradient(135deg, #4a1d1f 0%, #fdf9ed 100%); */
  background-image: url("https://static.wixstatic.com/media/d307ba_417f61b3c8bf448cad013986943a235f~mv2.jpeg/v1/fit/w_1151,h_775,q_90/d307ba_417f61b3c8bf448cad013986943a235f~mv2.webp");
  background-size: cover;
  border-radius: 6px;
  padding: 25px;
  text-decoration: none;
  outline: none;
  user-select: none;
}
.Callout:focus {
  box-shadow: 0 0 0 2px #4a1d1f;
}

.CalloutHeading {
  color: #fdf9ed;
  font-size: 18px;
  font-weight: 500;
  line-height: 1.2;
  margin-top: 16px;
  margin-bottom: 7px;
}

.CalloutText {
  color: #fdf9ed;
  font-size: 14px;
  line-height: 1.3;
}

.ViewportPosition {
  position: absolute;
  display: flex;
  justify-content: center;
  width: 100%;
  top: 100%;
  left: 0;
  perspective: 2000px;
}

.CaretDown {
  position: relative;
  color: #fff;
  top: 1px;
  transition: transform 250ms ease;
}
[data-state="open"] > .CaretDown {
  transform: rotate(-180deg);
}

.Arrow {
  position: relative;
  top: 70%;
  background-color: #fff;
  width: 10px;
  height: 10px;
  transform: rotate(45deg);
  border-top-left-radius: 2px;
}

@keyframes enterFromRight {
  from {
    opacity: 0;
    transform: translateX(200px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes enterFromLeft {
  from {
    opacity: 0;
    transform: translateX(-200px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes exitToRight {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(200px);
  }
}

@keyframes exitToLeft {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(-200px);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: rotateX(-30deg) scale(0.9);
  }
  to {
    opacity: 1;
    transform: rotateX(0deg) scale(1);
  }
}

@keyframes scaleOut {
  from {
    opacity: 1;
    transform: rotateX(0deg) scale(1);
  }
  to {
    opacity: 0;
    transform: rotateX(-10deg) scale(0.95);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
