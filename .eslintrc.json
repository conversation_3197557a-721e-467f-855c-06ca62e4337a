{"extends": ["next", "next/core-web-vitals", "eslint:recommended"], "globals": {"React": "readonly"}, "rules": {"no-unused-vars": [1, {"args": "after-used", "argsIgnorePattern": "^_"}], "indent": ["error", 2], "semi": ["error", "always"], "quotes": ["error", "double"], "no-var": "error", "no-console": "warn", "no-undef": "error", "react/prop-types": "error", "no-constant-condition": "warn", "react/react-in-jsx-scope": "off", "react/jsx-filename-extension": [1, {"extensions": [".js", ".jsx"]}], "react/jsx-props-no-spreading": [1, {"custom": "ignore"}], "react/jsx-curly-spacing": [2, "never"], "default-param-last": "off"}}