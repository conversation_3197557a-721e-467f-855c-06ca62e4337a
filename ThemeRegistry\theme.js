import { createTheme } from "@mui/material/styles";
import "@fontsource-variable/montserrat";
import pxToRem from "./pxToRem";

// const worksans = Work_Sans({
//   weight: ["300", "400", "500", "600", "700", "800", "900"],
//   style: ["normal", "italic"],
//   subsets: ["latin",],
// });


const theme = createTheme({
  palette: {
    mode: "dark",
    primary: {
      main: "#67f756",
      light: "#a4fa9a",
      dark: "#67f710",
      contrastText: "#09090B",
    },
    secondary: {
      main: "#4a1d1f",
      light: "#f73da3",
      dark: "#1e0c0c",
      contrastText: "#fafafa",
    },
    background: {
      default: "#09090B",
      paper: "rgba(255, 255, 255, 0.05)",
    },
    text: {
      primary: "#fff",
      secondary: "#C6C6C6",
    },
    success: {
      main: "#00ba3e",
      light: "#33c162",
      dark: "#017b2c",
      contrastText: "#fafafa",
    },
    error: {
      main: "#c72e2e",
      light: "#c35555",
      dark: "#8a1e1e",
    },
  },
  typography: {
    fontFamily: "Montserrat Variable, sans-serif",
    fontSize: 16,
    htmlFontSize: 16,
    h1: {
      fontSize: pxToRem(44),
      fontWeight: 800,
      lineHeight: "1.1em",
      letterSpacing: "-0.02em",
    },
    h2: {
      fontSize: pxToRem(38),
      fontWeight: 700,
      lineHeight: 1.2,
      letterSpacing: "-0.01em",
    },
    h3: {
      fontSize: pxToRem(34),
      fontWeight: 600,
      letterSpacing: "-0.01em",
    },
    h4: {
      fontSize: pxToRem(30),
      fontWeight: 600,
      letterSpacing: "0.5px",
    },
    h5: {
      fontSize: pxToRem(26),
      fontWeight: 500,
    },
    h6: {
      fontSize: pxToRem(22),
      fontWeight: 500,
    },
    caption: {
      fontSize: pxToRem(12),
      fontWeight: 300,
      letterSpacing: 1,
    },
    button: {
      fontSize: pxToRem(16),
      fontWeight: 600,
      lineHeight: "normal",
      textTransform: "none",
      letterSpacing: "0.5px",
    }
  },
  components: {
    MuiCssBaseline: {
      styleOverrides: {
        backgroundColor: "transparent",
        backgroundImage: `
          radial-gradient(circle at 20% 80%, rgba(103, 247, 86, 0.05) 0%, transparent 50%),
          radial-gradient(circle at 80% 20%, rgba(74, 29, 31, 0.1) 0%, transparent 50%),
          linear-gradient(135deg, #09090B 0%, #0f0f0f 25%, #1a1a1a 50%, #0f0f0f 75%, #09090B 100%)
        `,
      }
    },
    MuiButton: {
      styleOverrides: {
        root: {
          padding: "12px 25px",
          borderRadius: "50px",
          fontWeight: 600,
          fontSize: pxToRem(16),
          textTransform: "none",
          letterSpacing: "0.5px",
          transition: "all 0.4s cubic-bezier(0.4, 0, 0.2, 1)",
        },
        contained: {
          backgroundColor: "#67f756",
          color: "#09090B",
          boxShadow: "0 4px 20px rgba(103, 247, 86, 0.3)",
          border: "2px solid transparent",
          "&:hover": {
            backgroundColor: "#a4fa9a",
            transform: "translateY(-3px)",
            boxShadow: "0 8px 30px rgba(103, 247, 86, 0.5)",
            border: "2px solid #67f756",
          },
        },
        outlined: {
          borderColor: "rgba(255, 255, 255, 0.3)",
          color: "white",
          borderWidth: "2px",
          backgroundColor: "rgba(255, 255, 255, 0.05)",
          backdropFilter: "blur(10px)",
          "&:hover": {
            backgroundColor: "rgba(103, 247, 86, 0.1)",
            borderColor: "#67f756",
            color: "#67f756",
            transform: "translateY(-3px)",
            boxShadow: "0 8px 25px rgba(103, 247, 86, 0.2)",
          },
        },
        outlinedPrimary: {
          color: "#67f756",
          transition: "all 0.4s cubic-bezier(0.4, 0, 0.2, 1)",
          border: "2px solid #67f756",
          borderRadius: "50px",
          backgroundColor: "rgba(103, 247, 86, 0.1)",
          backdropFilter: "blur(10px)",
          "&:hover": {
            backgroundColor: "#67f756",
            color: "#09090B",
            transform: "translateY(-3px)",
            boxShadow: "0 8px 30px rgba(103, 247, 86, 0.5)",
          }
        },
      }
    },
    MuiTooltip: {
      styleOverrides: {
        tooltip: {
          backgroundColor: "rgba(255, 255, 255, 0.1)",
          color: "#fff",
          backdropFilter: "blur(10px)",
          border: "1px solid rgba(103, 247, 86, 0.3)",
          borderRadius: "12px",
          fontSize: 12,
          fontWeight: 500,
        },
        arrow: {
          color: "rgba(255, 255, 255, 0.1)",
        }
      }
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          "& .MuiOutlinedInput-root": {
            backgroundColor: "rgba(255, 255, 255, 0.05)",
            backdropFilter: "blur(10px)",
            borderRadius: "12px",
            border: "1px solid rgba(255, 255, 255, 0.1)",
            transition: "all 0.3s ease",
            "&:hover": {
              backgroundColor: "rgba(255, 255, 255, 0.08)",
              border: "1px solid rgba(103, 247, 86, 0.3)",
            },
            "&.Mui-focused": {
              backgroundColor: "rgba(255, 255, 255, 0.1)",
              border: "1px solid #67f756",
              boxShadow: "0 0 20px rgba(103, 247, 86, 0.2)",
            },
          },
          "& .MuiInputLabel-root": {
            color: "#C6C6C6",
            fontWeight: 500,
            "&.Mui-focused": {
              color: "#67f756",
            },
          },
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          backgroundColor: "rgba(255, 255, 255, 0.05)",
          backdropFilter: "blur(10px)",
          border: "1px solid rgba(255, 255, 255, 0.1)",
          borderRadius: "16px",
        },
      },
    },
  },
});

export default theme;
