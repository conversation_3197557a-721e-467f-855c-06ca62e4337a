"use client";
import React from "react";
import "photoswipe/dist/photoswipe.css";
import propTypes from "prop-types";
import { Gallery, Item } from "react-photoswipe-gallery";
import Image from "next/image";
import { Grid, Container, Typography, Divider, Box } from "@mui/material";

const ImageGallery = ({ images }) => {
  return (
    <Box
      sx={{
        position: "relative",
        background: `
          radial-gradient(circle at 20% 80%, rgba(103, 247, 86, 0.05) 0%, transparent 50%),
          radial-gradient(circle at 80% 20%, rgba(74, 29, 31, 0.08) 0%, transparent 50%),
          linear-gradient(135deg, #09090B 0%, #0f0f0f 25%, #1a1a1a 50%, #0f0f0f 75%, #09090B 100%)
        `,
        py: { xs: 6, md: 8 },
        overflow: "hidden",
      }}
    >
      {/* Modern geometric background */}
      <Box
        sx={{
          position: "absolute",
          top: 0,
          left: 0,
          width: "100%",
          height: "100%",
          background: `
            linear-gradient(45deg, transparent 30%, rgba(103, 247, 86, 0.02) 30%, rgba(103, 247, 86, 0.02) 32%, transparent 32%),
            linear-gradient(-45deg, transparent 30%, rgba(103, 247, 86, 0.02) 30%, rgba(103, 247, 86, 0.02) 32%, transparent 32%)
          `,
          backgroundSize: "60px 60px",
          zIndex: 1,
        }}
      />

      {/* Floating orb */}
      <Box
        sx={{
          position: "absolute",
          top: "10%",
          left: "5%",
          width: "200px",
          height: "200px",
          background:
            "radial-gradient(circle, rgba(103, 247, 86, 0.06) 0%, transparent 70%)",
          borderRadius: "50%",
          filter: "blur(40px)",
          animation: "float 8s ease-in-out infinite",
        }}
      />

      <Container sx={{ position: "relative", zIndex: 2 }}>
        <Box sx={{ textAlign: "center", mb: 6 }}>
          {/* Modern badge */}
          <Box
            sx={{
              display: "inline-flex",
              alignItems: "center",
              gap: 1,
              backgroundColor: "rgba(103, 247, 86, 0.1)",
              border: "1px solid rgba(103, 247, 86, 0.3)",
              borderRadius: "50px",
              px: 3,
              py: 1,
              mb: 3,
              backdropFilter: "blur(10px)",
            }}
          >
            <Typography
              variant="body2"
              sx={{
                color: "#67f756",
                fontWeight: "600",
                fontSize: { xs: "0.9rem", md: "1rem" },
                letterSpacing: "0.5px",
              }}
            >
              Vibes Rooftop
            </Typography>
          </Box>

          <Typography variant="h1" textAlign="center" sx={{ mb: 2 }}>
            Gallerie
          </Typography>
          <Typography
            variant="h6"
            textAlign="center"
            color="text.secondary"
            sx={{ fontSize: { xs: "1.1rem", md: "1.3rem" } }}
          >
            Entdecken Sie unsere einzigartige Atmosphäre
          </Typography>
        </Box>

        <Gallery id="vibes-rooftop-gallery">
          <Grid container spacing={4}>
            {images &&
              images.length &&
              images.map((media) => {
                const { fields, sys } = media;
                const isVideo = fields.file.contentType.startsWith("video");

                return (
                  <Grid key={sys.id} item xs={12} sm={6} md={4} lg={3}>
                    <Box
                      sx={{
                        position: "relative",
                        borderRadius: "16px",
                        overflow: "hidden",
                        background: "rgba(255, 255, 255, 0.05)",
                        backdropFilter: "blur(10px)",
                        border: "1px solid rgba(255, 255, 255, 0.1)",
                        transition: "all 0.4s cubic-bezier(0.4, 0, 0.2, 1)",
                        "&:hover": {
                          transform: "translateY(-8px)",
                          boxShadow: "0 20px 40px rgba(0, 0, 0, 0.4)",
                          border: "1px solid rgba(103, 247, 86, 0.3)",
                        },
                      }}
                    >
                      {isVideo ? (
                        <Item
                          id={sys.id}
                          original={fields.file.url}
                          thumbnail={fields.file.url}
                          width="800"
                          height="800"
                        >
                          {({ ref, open }) => (
                            <div className="image-container">
                              <video
                                ref={ref}
                                onClick={open}
                                style={{
                                  position: "absolute",
                                  cursor: "pointer",
                                }}
                                controls
                                className="image"
                              >
                                <source
                                  src={fields.file.url}
                                  type={fields.file.contentType}
                                />
                                Your browser does not support the video tag.
                              </video>
                              {/* Modern overlay effect */}
                              <Box
                                sx={{
                                  position: "absolute",
                                  top: 0,
                                  left: 0,
                                  width: "100%",
                                  height: "100%",
                                  background: `
                                  linear-gradient(135deg,
                                    rgba(103,247,86,0.05) 0%,
                                    transparent 30%,
                                    transparent 70%,
                                    rgba(74,29,31,0.05) 100%
                                  )
                                `,
                                  pointerEvents: "none",
                                }}
                              />
                            </div>
                          )}
                        </Item>
                      ) : (
                        <Item
                          id={sys.id}
                          original={fields.file.url}
                          thumbnail={fields.file.url}
                          width="800"
                          height="800"
                        >
                          {({ ref, open }) => (
                            <div className="image-container">
                              <Image
                                ref={ref}
                                onClick={open}
                                src={fields.file.url}
                                fill
                                alt="Vibes Rooftop Gallery"
                                className="image"
                                style={{ cursor: "pointer" }}
                              />
                              {/* Modern overlay effect */}
                              <Box
                                sx={{
                                  position: "absolute",
                                  top: 0,
                                  left: 0,
                                  width: "100%",
                                  height: "100%",
                                  background: `
                                  linear-gradient(135deg,
                                    rgba(103,247,86,0.05) 0%,
                                    transparent 30%,
                                    transparent 70%,
                                    rgba(74,29,31,0.05) 100%
                                  )
                                `,
                                  pointerEvents: "none",
                                }}
                              />
                            </div>
                          )}
                        </Item>
                      )}
                    </Box>
                  </Grid>
                );
              })}
          </Grid>
        </Gallery>
      </Container>
    </Box>
  );
};

export default ImageGallery;

ImageGallery.propTypes = {
  images: propTypes.array.isRequired,
};
