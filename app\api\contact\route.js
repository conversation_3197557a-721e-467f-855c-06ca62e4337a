// eslint-disable-next-line no-unused-vars
import { NextResponse, NextRequest } from "next/server";
import { mailOptions, transporter } from "@/config/kotakt";

export async function POST(req) {
  const body = await req.json();

  if (req.method !== "POST") {
    return new NextResponse({
      status: 405,
      body: { error: "Method Not Allowed" },
    });
  }

  const { name, lastName, email, phone, text } = body;

  // Define the email content for your email
  mailOptions.subject = "Kontakt Form";
  mailOptions.html = `
    <html>
      <body>
        <div style="display: grid; grid-template-columns: auto 1fr; grid-gap: 10px;">
          <p><b>Name:</b> ${name}</p>
          <p><b>Last Name:</b> ${lastName}</p>
          <p><b>Email:</b> ${email}</p>
          <p><b>Phone:</b> <a href="tel:${phone}">${phone}</a></p>
          <p><b>Message:</b> ${text}</p>
        </div>
      </body>
    </html>
  `;

  // Define the confirmation email content for the user
  const userMailOptions = {
    from: mailOptions.from,
    to: email, // Send to user's email
    subject: "Vielen Dank, dass Sie uns kontaktiert haben!",
    html: `
      <html>
      <body>
        <p>Sehr geehrte/r ${name},</p>
        <p>Vielen Dank, dass Sie uns kontaktiert haben. Wir haben Ihre Nachricht erhalten und werden uns so schnell wie möglich bei Ihnen melden.</p>
        <br />
        <p>Mit freundlichen Grüßen,<br/>Vibes Rooftop Team</p>
      </body>
    </html>
    `,
  };

  try {
    // Send the main email
    const info = await transporter.sendMail(mailOptions);
    console.log("Message sent to admin: %s", info.messageId);

    // Send the confirmation email to the user
    const userInfo = await transporter.sendMail(userMailOptions);
    console.log("Confirmation email sent to user: %s", userInfo.messageId);

    return new NextResponse({
      status: 200,
      body: { success: true },
    });
  } catch (error) {
    console.error(error);
    return new NextResponse({
      status: 500,
      body: { error: "Email sending failed" },
    });
  }
}

