import { mailOptions, transporter } from "@/config/kotakt";
// eslint-disable-next-line no-unused-vars
import { NextResponse, NextRequest } from "next/server";

export async function POST(req) {
  const body = await req.json();

  if (req.method !== "POST") {
    return new NextResponse({
      status: 405,
      body: { error: "Method Not Allowed" },
    });
  }

  const {
    name,
    Nachname,
    Reservierungsart,
    guests,
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    email,
    Telefon,
    Notiz,
  } = body;

  // Define the admin email content
  mailOptions.subject = "Reservierung Form";
  mailOptions.html = `
    <html>
      <body>
        <div style="display: grid; grid-template-columns: auto 1fr; grid-gap: 10px;">
          <p style="font-weight: bold; font-size: 1.2rem;"><span style="font-weight: 200; margin-right: 5px;">Name: </span>${name}</p>
          <p style="font-weight: bold; font-size: 1.2rem;"><span style="font-weight: 200; margin-right: 5px;">Nachname: </span>${Nachname}</p>
          <p style="font-weight: bold; font-size: 1.2rem;"><span style="font-weight: 200; margin-right: 5px;">Reservierungsart: </span>${Reservierungsart}</p>
          <p style="font-weight: bold; font-size: 1.2rem;"><span style="font-weight: 200; margin-right: 5px;">Gäste: </span>${guests}</p>
          <p style="font-weight: bold; font-size: 1.2rem;"><span style="font-weight: 200; margin-right: 5px;">Datum: </span>${Datum}</p>
          <p style="font-weight: bold; font-size: 1.2rem;"><span style="font-weight: 200; margin-right: 5px;">Uhr: </span>${Uhr}</p>
          <p style="font-weight: bold; font-size: 1.2rem;"><span style="font-weight: 200; margin-right: 5px;">E-mail: </span>${email}</p>
          <p style="font-weight: bold; font-size: 1.2rem;"><span style="font-weight: 200; margin-right: 5px;">Telefon: </span>${Telefon}</p>
          <p style="font-weight: bold; font-size: 1.2rem;"><span style="font-weight: 200; margin-right: 5px;">Notiz: </span>${Notiz}</p>
        </div>
      </body>
    </html>
  `;

  // Define the confirmation email content for the user
  const userMailOptions = {
    from: mailOptions.from,
    to: email,
    subject: "Vielen Dank für Ihre Reservierung bei Vibes Rooftop!",
    html: `
      <html>
        <body>
          <p>Sehr geehrte/r ${name} ${Nachname},</p>
          <p>Vielen Dank für Ihre Reservierung. Wir haben Ihre Anfrage erhalten und freuen uns, Sie bald bei Vibes Rooftop begrüßen zu dürfen.</p>
          <p><b>Details Ihrer Reservierung:</b></p>
          <ul>
            <li><b>Reservierungsart:</b> ${Reservierungsart}</li>
            <li><b>Gäste:</b> ${guests}</li>
            <li><b>Datum:</b> ${Datum}</li>
            <li><b>Uhrzeit:</b> ${Uhr}</li>
          </ul>
          <br />
          <p>Bei weiteren Fragen stehen wir Ihnen gerne zur Verfügung.</p>
          <p>Mit freundlichen Grüßen,<br/>Vibes Rooftop</p>
        </body>
      </html>
    `,
  };

  try {
    // Send the email to the admin
    const info = await transporter.sendMail(mailOptions);
    console.log("Admin email sent: %s", info.messageId);

    // Send the confirmation email to the user
    const userInfo = await transporter.sendMail(userMailOptions);
    console.log("Confirmation email sent to user: %s", userInfo.messageId);

    return new NextResponse({
      status: 200,
      body: { success: true },
    });
  } catch (error) {
    console.error("Error sending email:", error);
    return new NextResponse({
      status: 500,
      body: { error: "Email sending failed" },
    });
  }
}
