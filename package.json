{"name": "vibes-rooftop", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "postbuild": "next-sitemap", "start": "next start", "lint": "next lint", "prettier": "prettier --write ."}, "dependencies": {"@emotion/cache": "latest", "@emotion/react": "latest", "@emotion/styled": "latest", "@fontsource-variable/montserrat": "^5.0.16", "@mui/lab": "^5.0.0-alpha.146", "@mui/material": "latest", "@radix-ui/react-navigation-menu": "^1.1.4", "contentful": "^10.6.1", "formik": "^2.4.5", "mapbox-gl": "^2.15.0", "next": "latest", "next-pwa": "^5.6.0", "next-sitemap": "^4.2.3", "nodemailer": "^6.9.7", "react": "latest", "react-dom": "latest", "react-icons": "^4.11.0", "react-photoswipe-gallery": "^2.2.7", "yup": "^1.3.2"}, "devDependencies": {"eslint": "latest", "eslint-config-next": "latest", "prettier": "^3.0.3"}}