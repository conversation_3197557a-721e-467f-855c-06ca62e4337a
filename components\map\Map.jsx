"use client";
/* eslint-disable import/no-extraneous-dependencies */
import React, { useEffect, useRef } from "react";
import propTypes from "prop-types";
import mapboxgl from "mapbox-gl";
import "mapbox-gl/dist/mapbox-gl.css";

function Map({value, direction}) {
  const mapContainerRef = useRef(null);
  mapboxgl.accessToken = process.env.NEXT_PUBLIC_MAPBOX_TOKEN;

  useEffect(() => {
    const map = new mapboxgl.Map({
      container: mapContainerRef.current,
      style: "mapbox://styles/mapbox/streets-v12",
      center: value,
      zoom: 17,
    });

    const navControl = new mapboxgl.NavigationControl();
    map.addControl(navControl, "top-right");

    const markers = [
      {
        lngLat: value,
        color: "#00704A",
        address: null,
      },
    ];

    markers.forEach((marker) => {
      const { lngLat, color } = marker;
      const popup = new mapboxgl.Popup({ closeOnClick: false })
        .setHTML(direction)
        .addTo(map);

      const markerInstance = new mapboxgl.Marker({
        color,
      })
        .setLngLat(lngLat)
        .addTo(map);

      markerInstance.setPopup(popup);

      const fetchAddress = async () => {
        try {
          const response = await fetch(
            `https://api.mapbox.com/geocoding/v5/mapbox.places/${lngLat[0]},${lngLat[1]}.json?access_token=${mapboxgl.accessToken}`,
          );
          const data = await response.json();
          const address = data.features[0].place_name;
          // eslint-disable-next-line no-param-reassign
          marker.address = address;
        } catch (error) {
          // eslint-disable-next-line no-console
          console.log("Fehler beim Abrufen der Adresse: ", error);
        }
      };

      markerInstance.on("click", async () => {
        if (!marker.address) {
          await fetchAddress();
        }
        // eslint-disable-next-line no-unused-expressions
      }, { passive: true });
    });

    return () => map.remove();
  }, [value, direction]);

  return (
    <>
      <div ref={mapContainerRef} style={{ width: "100%", height: "350px" }} />
    </>
  );
}

export default Map;

Map.propTypes = {
  value: propTypes.array.isRequired,
  direction: propTypes.string.isRequired,
};