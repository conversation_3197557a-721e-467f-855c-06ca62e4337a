"use client";

import React, { useState } from "react";
import Navigation from "./Navigation";
import Image from "next/image";
import AppBar from "@mui/material/AppBar";
import Link from "next/link";
import List from "@mui/material/List";
import ListItem from "@mui/material/ListItem";
import Drawer from "@mui/material/Drawer";
import Toolbar from "@mui/material/Toolbar";
import IconButton from "@mui/material/IconButton";
import { Box, Container, Typography } from "@mui/material";
import { RiMenu4Line } from "react-icons/ri";
import { AiOutlineClose } from "react-icons/ai";
import { BiHomeAlt } from "react-icons/bi";
// import { BsMenuUp } from "react-icons/bs";
import { LuGalleryVerticalEnd } from "react-icons/lu";
import { BsInfoSquare } from "react-icons/bs";
import { MdOutlineContacts } from "react-icons/md";

const drawerWidth = "100%";

function Header() {
  const [isMobileMenuOpen, setMobileMenuOpen] = useState(false);

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!isMobileMenuOpen);
  };

  return (
    <div style={{ margin: "70px 0px", backgroundColor: "transparent" }}>
      {/* Large Screen AppBar */}
      <AppBar
        position="fixed"
        component="div"
        sx={{
          display: { xs: "none", md: "block" },
          background:
            "linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05))",
          backdropFilter: "blur(25px) saturate(180%)",
          WebkitBackdropFilter: "blur(25px) saturate(180%)",
          border: "1px solid rgba(255, 255, 255, 0.18)",
          borderTop: "none",
          borderLeft: "none",
          borderRight: "none",
          borderBottom: "1px solid rgba(255, 255, 255, 0.2)",
          color: "text.secondary",
          py: 1,
          boxShadow:
            "0 8px 32px rgba(31, 38, 135, 0.37), inset 0 1px 0 rgba(255, 255, 255, 0.2)",
          transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
          "&::before": {
            content: '""',
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background:
              "linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 50%, rgba(255, 255, 255, 0.02) 100%)",
            borderRadius: "inherit",
            zIndex: -1,
          },
          "&:hover": {
            background:
              "linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.08))",
            backdropFilter: "blur(30px) saturate(200%)",
            WebkitBackdropFilter: "blur(30px) saturate(200%)",
            boxShadow:
              "0 12px 40px rgba(31, 38, 135, 0.5), inset 0 1px 0 rgba(255, 255, 255, 0.3)",
          },
        }}
      >
        <Container sx={{ maxWidth: { xs: "xs", md: "xl" } }}>
          <Toolbar sx={{ justifyContent: "space-between" }}>
            <Link href={"/"}>
              <Image src="/vibes.webp" width={140} height={40} alt="Logo" />
            </Link>
            <Navigation />
          </Toolbar>
        </Container>
      </AppBar>

      {/* Mobile Screen Drawer */}
      <AppBar
        position="fixed"
        component="div"
        sx={{
          display: { xs: "block", md: "none" },
          background:
            "linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05))",
          backdropFilter: "blur(25px) saturate(180%)",
          WebkitBackdropFilter: "blur(25px) saturate(180%)",
          border: "1px solid rgba(255, 255, 255, 0.18)",
          borderTop: "none",
          borderLeft: "none",
          borderRight: "none",
          borderBottom: "1px solid rgba(255, 255, 255, 0.2)",
          color: "text.secondary",
          py: 1,
          boxShadow:
            "0 8px 32px rgba(31, 38, 135, 0.37), inset 0 1px 0 rgba(255, 255, 255, 0.2)",
          transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
          "&::before": {
            content: '""',
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background:
              "linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 50%, rgba(255, 255, 255, 0.02) 100%)",
            borderRadius: "inherit",
            zIndex: -1,
          },
        }}
      >
        <Toolbar>
          <Box sx={{ mt: 1, p: 0.5, flexGrow: 1 }}>
            <Link href={"/"}>
              <Image src="/vibes.webp" width={100} height={30} alt="Logo" />
            </Link>
          </Box>
          <IconButton
            size="medium"
            color="inherit"
            onClick={toggleMobileMenu}
            sx={{ zIndex: 3 }}
          >
            {!isMobileMenuOpen ? <RiMenu4Line /> : <AiOutlineClose />}
          </IconButton>
        </Toolbar>
      </AppBar>
      <Drawer
        sx={{
          display: { xs: "block", md: "none" },
          width: drawerWidth,
          zIndex: 2,
          flexShrink: 0,
          "& .MuiDrawer-paper": {
            width: drawerWidth,
            background:
              "linear-gradient(135deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.03))",
            backdropFilter: "blur(30px) saturate(180%)",
            WebkitBackdropFilter: "blur(30px) saturate(180%)",
            border: "1px solid rgba(255, 255, 255, 0.15)",
            borderTop: "none",
            color: "#ffffff",
            boxSizing: "border-box",
            mt: 8.3,
            boxShadow:
              "0 8px 32px rgba(31, 38, 135, 0.37), inset 0 1px 0 rgba(255, 255, 255, 0.1)",
            "&::before": {
              content: '""',
              position: "absolute",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background:
                "linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 50%, rgba(255, 255, 255, 0.01) 100%)",
              borderRadius: "inherit",
              zIndex: -1,
            },
          },
        }}
        anchor="top"
        open={isMobileMenuOpen}
        onClose={toggleMobileMenu}
      >
        <List>
          <Link href={"/"}>
            <ListItem onClick={toggleMobileMenu} button>
              <Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
                <IconButton size="small">
                  <BiHomeAlt />
                </IconButton>
                <Typography variant="body1" pt={0.5}>
                  Home
                </Typography>
              </Box>
            </ListItem>
          </Link>
          {/* <Link href={"/menuekarte"}>
            <ListItem onClick={toggleMobileMenu} button>
              <Box sx={{display: "flex", alignItems: "center", gap: 0.5}}>
                <IconButton size="small">
                  <BsMenuUp />
                </IconButton>
                <Typography variant="body1" pt={0.5}>
                Menükarte
                </Typography>
              </Box>
            </ListItem>
          </Link> */}
          <Link href={"/gallerie"}>
            <ListItem onClick={toggleMobileMenu} button>
              <Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
                <IconButton size="small">
                  <LuGalleryVerticalEnd />
                </IconButton>
                <Typography variant="body1" pt={0.5}>
                  Gallerie
                </Typography>
              </Box>
            </ListItem>
          </Link>
          <Link href={"/uber-uns"}>
            <ListItem onClick={toggleMobileMenu} button>
              <Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
                <IconButton size="small">
                  <BsInfoSquare />
                </IconButton>
                <Typography variant="body1" pt={0.5}>
                  Über uns
                </Typography>
              </Box>
            </ListItem>
          </Link>
          <Link href={"/kontakt"}>
            <ListItem onClick={toggleMobileMenu} button>
              <Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
                <IconButton size="small">
                  <MdOutlineContacts />
                </IconButton>
                <Typography variant="body1" pt={0.5}>
                  Kontakt
                </Typography>
              </Box>
            </ListItem>
          </Link>
        </List>
      </Drawer>
    </div>
  );
}

export default Header;
