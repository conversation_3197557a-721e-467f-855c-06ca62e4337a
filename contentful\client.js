const contentful = require("contentful");

function isServer() {
  return !(typeof window != "undefined" && window.document);
}

const serverOnlyCreateClient = () => {
  if (!isServer()) return;

  const client = contentful.createClient({
    space: process.env.CONTENTFUL_SPACE_ID,
    environment: "master",
    accessToken: process.env.CONTENTFUL_ACCESS_TOKEN
  });

  return client;
};

export const client = serverOnlyCreateClient();