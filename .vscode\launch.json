{"version": "0.1.0", "configurations": [{"name": "Next.js: debug server-side", "type": "node-terminal", "request": "launch", "command": "npm run dev"}, {"name": "Next.js: debug client-side", "type": "pwa-chrome", "request": "launch", "url": "http://localhost:3000"}, {"name": "Next.js: debug full stack", "type": "node-terminal", "request": "launch", "command": "npm run dev", "console": "integratedTerminal", "serverReadyAction": {"pattern": "started server on .+, url: (https?://.+)", "uriFormat": "%s", "action": "debugWithChrome"}}]}