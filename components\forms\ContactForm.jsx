"use client";

import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Typo<PERSON> } from "@mui/material";
import { sendContactForm } from "@/lib/api";
import { LoadingButton } from "@mui/lab";
import { BsSend } from "react-icons/bs";

const ContactForm = () => {
  const [formData, setFormData] = useState({
    name: "",
    lastName: "",
    email: "",
    phone: "",
    text: "",
  });

  const [errors, setErrors] = useState({
    email: "",
    phone: "",
  });

  const [submitMessage, setSubmitMessage] = useState();
  const [loading, setLoading] = useState(false);

  const handleChange = (event) => {
    const { id, value } = event.target;
    setFormData((prevData) => ({
      ...prevData,
      [id]: value,
    }));
  };

  async function handleSubmit(event) {
    event.preventDefault();
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      setErrors((prevErrors) => ({
        ...prevErrors,
        email: "Invalid email address",
      }));
      return;
    } else {
      setErrors((prevErrors) => ({
        ...prevErrors,
        email: "",
      }));
    }

    // Phone number validation using regular expression (allowing only digits and dashes)
    const phoneRegex = /^\d+(-\d+)*$/;
    if (!phoneRegex.test(formData.phone)) {
      setErrors((prevErrors) => ({
        ...prevErrors,
        phone: "Invalid phone number",
      }));
      return;
    } else {
      setErrors((prevErrors) => ({
        ...prevErrors,
        phone: "",
      }));
    }
    // Perform form submission logic here, e.g., sending data to an API
    // eslint-disable-next-line no-console
    try {
      setLoading(true);
      const response = await sendContactForm(formData);
      if (response.status === 200) {
        // Handle successful response here
        setLoading(false);
        setSubmitMessage(
          <Alert
            sx={{
              fontSize: {
                xs: "15px",
                sm: "17px",
                md: "20px",
              },
              fontWeight: "600",
            }}
          >
            Vielen Dank für Ihre Anfrage! <br /> Wir werden uns umgehend bei
            Ihnen melden
          </Alert>
        );
      } else {
        // Handle error response here
        setSubmitMessage(`error ${response.status}: ${response.statusText}`);
      }
    } catch (error) {
      // Handle network error here
      setSubmitMessage(`Ein Fehler ist aufgetreten: ${error}`);
    }
  }

  return (
    <Box
      sx={{
        position: "relative",
        background: "rgba(255, 255, 255, 0.05)",
        backdropFilter: "blur(15px)",
        border: "1px solid rgba(255, 255, 255, 0.1)",
        borderRadius: "20px",
        p: { xs: 3, md: 4 },
        boxShadow: "0 8px 32px rgba(0, 0, 0, 0.3)",
        overflow: "hidden",
      }}
    >
      {/* Modern geometric background */}
      <Box
        sx={{
          position: "absolute",
          top: 0,
          left: 0,
          width: "100%",
          height: "100%",
          background: `
            linear-gradient(45deg, transparent 30%, rgba(103, 247, 86, 0.01) 30%, rgba(103, 247, 86, 0.01) 32%, transparent 32%),
            linear-gradient(-45deg, transparent 30%, rgba(103, 247, 86, 0.01) 30%, rgba(103, 247, 86, 0.01) 32%, transparent 32%)
          `,
          backgroundSize: "40px 40px",
          zIndex: 1,
        }}
      />

      {!submitMessage ? (
        <Box sx={{ position: "relative", zIndex: 2 }}>
          <form onSubmit={handleSubmit}>
            <Grid container spacing={2} my={1}>
              <Grid item xs={6}>
                <TextField
                  required
                  fullWidth
                  id="name"
                  label="Name"
                  variant="outlined"
                  size="small"
                  value={formData.name}
                  onChange={handleChange}
                />
              </Grid>
              <Grid item xs={6}>
                <TextField
                  required
                  fullWidth
                  id="lastName"
                  label="Last Name"
                  variant="outlined"
                  size="small"
                  value={formData.lastName}
                  onChange={handleChange}
                />
              </Grid>
              <Grid item xs={6}>
                <TextField
                  required
                  fullWidth
                  id="email"
                  label="Email"
                  variant="outlined"
                  size="small"
                  value={formData.email}
                  onChange={handleChange}
                  error={Boolean(errors.email)}
                  helperText={errors.email}
                />
              </Grid>
              <Grid item xs={6}>
                <TextField
                  required
                  fullWidth
                  id="phone"
                  label="Phone"
                  variant="outlined"
                  size="small"
                  value={formData.phone}
                  onChange={handleChange}
                  error={Boolean(errors.phone)}
                  helperText={errors.phone}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  id="text"
                  label="Text"
                  variant="outlined"
                  size="small"
                  multiline
                  rows={5}
                  value={formData.text}
                  onChange={handleChange}
                />
              </Grid>
              <Grid item xs={12}>
                <LoadingButton
                  type="submit"
                  endIcon={<BsSend />}
                  loading={loading}
                  loadingPosition="end"
                  variant="contained"
                  size="large"
                  sx={{
                    px: 6,
                    py: 2,
                    fontSize: "1.1rem",
                    mt: 1,
                  }}
                >
                  <Typography>Nachricht senden</Typography>
                </LoadingButton>
              </Grid>
            </Grid>
          </form>
        </Box>
      ) : (
        <Box
          sx={{ position: "relative", zIndex: 2, textAlign: "center", py: 4 }}
        >
          {submitMessage}
        </Box>
      )}
    </Box>
  );
};

export default ContactForm;
